"use strict";

module.exports = (sequelize, DataTypes) => {
  const Role = sequelize.define(
    "Role",
    {
      roleDisplayName: {
        type: DataTypes.STRING,
        defaultValue: "",
      },
      role: {
        type: DataTypes.STRING,
        defaultValue: "user",
        // validate: {
        //   isIn: [['admin', 'user', 'moderator']] // Adjust this according to userRoles values
        // }
      },
      desc: {
        type: DataTypes.STRING,
        defaultValue: "",
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
    },
  );

  Role.associate = (models) => {
    Role.hasMany(models.User, { foreignKey: "role", as: "users" });
    Role.hasMany(models.RolePermission, { foreignKey: "roleId", as: "rolePermissions" });
  };
  return Role;
};
