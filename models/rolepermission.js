"use strict";

module.exports = (sequelize, DataTypes) => {
  const RolePermission = sequelize.define(
    "RolePermission",
    {
      roleId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: { model: "Roles", key: "id" },
      },
      permissionId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: { model: "Permissions", key: "id" },
      },
      status: {
        type: DataTypes.ENUM("active", "inactive"),
        defaultValue: "active",
        allowNull: false,
      },
    },
    {
      timestamps: true,
    },
  );

  RolePermission.associate = (models) => {
    RolePermission.belongsTo(models.Role, {
      foreignKey: "roleId",
      as: "Role"
    });
    RolePermission.belongsTo(models.Permission, {
      foreignKey: "permissionId",
      as: "Permission"
    });
  };

  return RolePermission;
};
