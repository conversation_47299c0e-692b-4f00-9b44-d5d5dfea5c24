"use strict";

module.exports = (sequelize, DataTypes) => {
  const Post = sequelize.define(
    "Post",
    {
      user_id: {
        type: DataTypes.INTEGER,
      },
      group_id: {
        type: DataTypes.INTEGER,
      },
      content: {
        type: DataTypes.TEXT,
      },
      media_url: {
        type: DataTypes.JSON,
        defaultValue: [],
      },
      hash_tags: {
        type: DataTypes.JSON,
        defaultValue: [],
      },
      location: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      latitude: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      longitude: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.DATE,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    {
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    },
  );

  Post.associate = (models) => {
    Post.belongsTo(models.User, { foreignKey: "user_id", as: "user" });
    Post.hasMany(models.Like, { foreignKey: "post_id", as: "likes" });
    Post.hasMany(models.Comment, { foreignKey: "post_id", as: "comments" });
  };
  return Post;
};
