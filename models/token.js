"use strict";

module.exports = (sequelize, DataTypes) => {
  const Token = sequelize.define(
    "Token",
    {
      email: {
        type: DataTypes.STRING,
      },
      user_id: {
        type: DataTypes.INTEGER,
      },
      password: {
        type: DataTypes.STRING,
      },
      mobile_number: {
        type: DataTypes.STRING,
      },
      token: {
        type: DataTypes.STRING,
      },
      expires: {
        type: DataTypes.DATE,
      },
      type: {
        type: DataTypes.STRING,
      },
      otp: {
        type: DataTypes.STRING,
      },
    },
    {
      timestamps: true,
    },
  );
  return Token;
};
