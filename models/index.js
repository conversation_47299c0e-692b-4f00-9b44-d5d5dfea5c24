const fs = require("fs");
const path = require("path");
const { sequelize } = require("../config/db-config");
const Sequelize = require("sequelize");

const db = {};
const modelsDir = __dirname;

fs.readdirSync(modelsDir)
  .filter((file) => file.endsWith(".js") && file !== "index.js")
  .forEach((file) => {
    const model = require(path.join(modelsDir, file))(
      sequelize,
      Sequelize.DataTypes,
    );
    db[model.name] = model;
  });

Object.keys(db).forEach((modelName) => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

db.sequelize = sequelize;
db.Sequelize = Sequelize;

module.exports = db;
