"use strict";

module.exports = (sequelize, DataTypes) => {
  const Ad = sequelize.define(
    "Ad",
    {
      ad_title: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      advertiser_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      ad_type: {
        type: DataTypes.ENUM("Banner", "Video", "Pop-up"),
        allowNull: false,
      },
      ad_image: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      ad_placement: {
        type: DataTypes.ENUM(
          "Homepage",
          "Sidebar",
          "Feed",
          "Footer",
          "Pop-up",
          "Story Ads",
        ),
        allowNull: false,
      },
      category: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      target_audience: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      ad_media: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      start_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      end_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM("Active", "Inactive"),
        defaultValue: "Active",
      },
      clicks: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
    },
    {
      timestamps: true,
    },
  );

  return Ad;
};
