const { User, Role } = require("../models");
const { sendError, sendSuccess } = require("../jsonResponse");
const { userSearchFields } = require("../utils/constant");
const { buildMatchQuery, getPaginationMetaData } = require("./helper");

const getCustomersList = async (req, res) => {
  try {
    const { whereClause, orderClause, skip, limit } = buildMatchQuery(
      req,
      {},
      userSearchFields,
    );

    const { rows: users, count: totalCount } = await User.findAndCountAll({
      where: whereClause,
      order: Object.entries(orderClause),
      offset: skip,
      limit: limit,
      include: [
        {
          model: Role,
          as: "userRole",
          attributes: ["role", "roleDisplayName"],
          where: {
            role: "user", // For role ONLY "user"
          },
        },
      ],
      raw: true,
    });

    const metadata = getPaginationMetaData(totalCount, skip, limit);

    sendSuccess(res, "Users retrieved successfully", 200, {
      users,
      metadata,
    });
  } catch (error) {
    sendError(res, error?.message || "Failed to get user list", 500, error);
  }
};

const getCustomerDetail = async (req, res) => {
  const { userId } = req.body;
  try {
    const user = await User.findOne({
      where: { id: userId },
      include: [
        {
          model: Role,
          as: "userRole",
          attributes: ["role", "roleDisplayName"],
        },
      ],
    });

    if (!user) {
      return sendError(res, "user not found", 404);
    }

    sendSuccess(res, "Fetch user data successful", 200, user);
  } catch (error) {
    sendError(res, error?.message || "Failed to fetch user data", 500, error);
  }
};

const createCustomer = async (req, res) => {
  const {
    first_name,
    last_name,
    email,
    mobile_number,
    password,
    accountStatus,
  } = req.body;
  try {
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return sendError(res, "Email is already in use", 400);
    }

    const userRole = await Role.findOne({
      where: { role: "user", isDeleted: false },
    });
    if (!userRole) {
      return sendError(res, "User role not found", 400);
    }

    const randomNumber = Math.floor(10 + Math.random() * 90);
    const username = `${first_name.toLowerCase()}${last_name.toLowerCase()}${randomNumber}`;

    const newUser = await User.create({
      username,
      first_name,
      last_name,
      email,
      mobile_number,
      password_hash: password,
      role: userRole.id,
      status: accountStatus,
    });

    sendSuccess(res, "Customer created successfully", 201, {
      customer: newUser,
    });
  } catch (error) {
    sendError(res, error?.message || "Failed to add user", 500, error);
  }
};

const editCustomer = async (req, res) => {
  const {
    userId,
    first_name,
    last_name,
    email,
    mobile_number,
    password,
    role,
    accountStatus,
  } = req.body;
  try {
    const user = await User.findOne({ where: { id: userId } });

    if (!user) {
      return sendError(res, "User not found", 404);
    }

    user.first_name = first_name;
    user.last_name = last_name;
    user.email = email;
    user.mobile_number = mobile_number;
    user.role = role;
    user.status = accountStatus;

    if (password) {
      user.password_hash = password;
    }

    await user.save();

    sendSuccess(res, "User Updated successfully", 200, { user });
  } catch (error) {
    sendError(res, error?.message || "Failed to edit user", 500, error);
  }
};

const deleteCustomer = async (req, res) => {
  const { userId } = req.body;
  try {
    const user = await User.findByPk(userId);
    if (!user) {
      return sendError(res, "User not found", 404);
    }

    user.status = "Inactive";
    user.deleted_at = new Date();

    await user.save();

    sendSuccess(res, "User has been marked as inactive", 200, { user });
  } catch (error) {
    sendError(res, error?.message || "Failed to delete user", 500, error);
  }
};

module.exports = {
  getCustomersList,
  getCustomerDetail,
  createCustomer,
  editCustomer,
  deleteCustomer,
};
