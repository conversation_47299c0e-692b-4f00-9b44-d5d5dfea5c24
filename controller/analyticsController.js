const { User, Post, Like, Comment } = require("../models");
const { Op, fn, col, literal } = require("sequelize");
const moment = require("moment");
const { sendError, sendSuccess } = require("../jsonResponse");

const rangeMapping = {
  "1h": 1,
  "24h": 24,
  "7d": 24 * 7,
  "30d": 24 * 30,
  "90d": 24 * 90,
  "365d": 24 * 365,
};

const getDateRanges = (hours) => {
  const now = new Date();
  const currentStart = new Date(now.getTime() - hours * 60 * 60 * 1000);
  const previousStart = new Date(
    currentStart.getTime() - hours * 60 * 60 * 1000,
  );
  return { now, currentStart, previousStart };
};

const getPercentageChange = (current, previous) => {
  if (previous === 0) return current === 0 ? 0 : 100;
  return ((current - previous) / previous) * 100;
};

const getMetricsForPeriod = async (createdAtCondition, loginAtCondition) => {
  const [activeUsers, newUsers, totalPosts, likesCount, commentsCount] =
    await Promise.all([
      User.count({
        where: {
          [Op.and]: [literal(loginAtCondition), { role: 2 }],
        },
      }),
      User.count({
        where: {
          [Op.and]: [literal(createdAtCondition), { role: 2 }],
        },
      }),
      Post.count({
        where: {
          [Op.and]: [literal(createdAtCondition), { is_deleted: false }],
        },
      }),
      Like.count({
        where: literal(createdAtCondition),
      }),
      Comment.count({
        where: literal(createdAtCondition),
      }),
    ]);

  return {
    activeUsers,
    newUsers,
    totalPosts,
    likesCount,
    commentsCount,
  };
};

const getTrendMetricsSummary = async () => {
  const [todayMetrics, weekMetrics, monthMetrics] = await Promise.all([
    // TODAY
    getMetricsForPeriod(
      `DATE(created_at) = CURDATE()`,
      `DATE(last_login_at) = CURDATE()`,
    ),
    // THIS WEEK
    getMetricsForPeriod(
      `YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`,
      `YEARWEEK(last_login_at, 1) = YEARWEEK(CURDATE(), 1)`,
    ),
    // THIS MONTH
    getMetricsForPeriod(
      `MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())`,
      `MONTH(last_login_at) = MONTH(CURDATE()) AND YEAR(last_login_at) = YEAR(CURDATE())`,
    ),
  ]);

  return {
    today: todayMetrics,
    thisWeek: weekMetrics,
    thisMonth: monthMetrics,
  };
};

const getLiveMetricsData = async (req, res) => {
  try {
    const { range = "7d" } = req.body;
    const hours = rangeMapping[range];
    const { now, currentStart, previousStart } = getDateRanges(hours);
    const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 5, 1);

    // total feeds
    const [currentFeedCount, previousFeedCount] = await Promise.all([
      Post.count({
        where: { created_at: { [Op.between]: [currentStart, now] } },
      }),
      Post.count({
        where: { created_at: { [Op.between]: [previousStart, currentStart] } },
      }),
    ]);

    const roleFilter = { role: 2, is_deleted: false };

    // total users
    const [currentUserCount, previousUserCount] = await Promise.all([
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [currentStart, now] },
        },
      }),
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [previousStart, currentStart] },
        },
      }),
    ]);

    // active users
    const [activeUserCount, previousActiveUserCount] = await Promise.all([
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [currentStart, now] },
          status: "active",
        },
      }),
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [previousStart, currentStart] },
          status: "active",
        },
      }),
    ]);

    // inactive users
    const [inactiveUserCount, previousInactiveUserCount] = await Promise.all([
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [currentStart, now] },
          status: "inactive",
        },
      }),
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [previousStart, currentStart] },
          status: "inactive",
        },
      }),
    ]);

    // geography wise split
    const geographySplit = await User.findAll({
      attributes: ["country", [fn("COUNT", col("country")), "count"]],
      where: {
        ...roleFilter,
        created_at: { [Op.between]: [currentStart, now] },
      },
      group: ["country"],
      raw: true,
    });

    // Calculate % change
    const feedChange = getPercentageChange(currentFeedCount, previousFeedCount);
    const userChange = getPercentageChange(currentUserCount, previousUserCount);
    const activeUserChange = getPercentageChange(
      activeUserCount,
      previousActiveUserCount,
    );
    const inactiveUserChange = getPercentageChange(
      inactiveUserCount,
      previousInactiveUserCount,
    );

    // Timeline Tracker
    const timeline = await Post.findAll({
      where: {
        created_at: { [Op.between]: [sixMonthsAgo, now] },
        is_deleted: false,
      },
      attributes: [
        [fn("YEAR", col("created_at")), "year"],
        [fn("MONTH", col("created_at")), "month"],
        [fn("GROUP_CONCAT", col("content")), "posts_content"],
      ],
      group: [fn("YEAR", col("created_at")), fn("MONTH", col("created_at"))],
      order: [
        [fn("YEAR", col("created_at")), "ASC"],
        [fn("MONTH", col("created_at")), "ASC"],
      ],
      raw: true,
    });

    // 2. Interactive Event List
    const eventListRaw = await Post.findAll({
      where: {
        created_at: { [Op.between]: [currentStart, now] },
        is_deleted: false,
      },
      attributes: [
        "id",
        "content",
        "created_at",
        [fn("COUNT", col("likes.user_id")), "likes"],
        [fn("COUNT", col("comments.user_id")), "comments"],
        [literal("0"), "views"],
        [literal("0"), "active_users"],
        [literal("0"), "duration"],
      ],
      include: [
        {
          model: User,
          as: "user",
          attributes: ["username"],
          where: roleFilter,
        },
        { model: Like, as: "likes", attributes: [] },
        { model: Comment, as: "comments", attributes: [] },
      ],
      group: ["Post.id", "user.id"],
      order: [["created_at", "DESC"]],
    });

    const eventList = await Promise.all(
      eventListRaw.map(async (post) => {
        const [likes, comments] = await Promise.all([
          Like.findAll({
            where: { post_id: post.id },
            attributes: ["user_id"],
            raw: true,
          }),
          Comment.findAll({
            where: { post_id: post.id },
            attributes: ["user_id"],
            raw: true,
          }),
        ]);

        const uniqueUserIds = new Set();
        likes.forEach((l) => uniqueUserIds.add(l.user_id));
        comments.forEach((c) => uniqueUserIds.add(c.user_id));

        post.setDataValue("active_users", uniqueUserIds.size);

        return post;
      }),
    );

    // 3. Engagement Trends: Posts & Live
    const engagements = await Post.findOne({
      where: {
        created_at: { [Op.between]: [sixMonthsAgo, now] },
        is_deleted: false,
      },
      attributes: [
        [fn("YEAR", col("created_at")), "year"],
        [fn("MONTH", col("created_at")), "month"],
        [fn("COUNT", col("Post.id")), "total_posts"],
        [
          literal(`IFNULL((
          SELECT COUNT(*) FROM Likes 
          WHERE Likes.created_at BETWEEN '${currentStart.toISOString()}' AND '${now.toISOString()}'
        ), 0)`),
          "total_likes",
        ],
        [
          literal(`IFNULL((
          SELECT COUNT(*) FROM Comments 
          WHERE Comments.created_at BETWEEN '${currentStart.toISOString()}' AND '${now.toISOString()}'
        ), 0)`),
          "total_comments",
        ],
        [literal("0"), "total_shares"],
        [literal("0"), "total_live_viewers"],
      ],
      group: [fn("YEAR", col("created_at")), fn("MONTH", col("created_at"))],
      order: [
        [fn("YEAR", col("created_at")), "ASC"],
        [fn("MONTH", col("created_at")), "ASC"],
      ],
      raw: true,
    });

    // 4. Trend Metrics
    const trendMetrics = await getTrendMetricsSummary();

    sendSuccess(res, "Live metrics retrieved successfully", 200, {
      feeds: {
        count: currentFeedCount,
        percentageChange: feedChange,
      },
      users: {
        count: currentUserCount,
        percentageChange: userChange,
      },
      activeUsers: {
        count: activeUserCount,
        percentageChange: activeUserChange,
      },
      inactiveUsers: {
        count: inactiveUserCount,
        percentageChange: inactiveUserChange,
      },
      geographySplit,
      timeline,
      eventList,
      engagements,
      trendMetrics,
    });
  } catch (error) {
    sendError(res, error?.message || "Failed to get live metrics", 500, error);
  }
};

const getMonthPeriod = (period) => {
  const today = moment().endOf("day");
  let startDate, endDate, prevStartDate, prevEndDate;

  const monthMap = {
    last3Months: 2,
    last6Months: 5,
    last12Months: 11,
  };

  if (period in monthMap) {
    const monthsToSubtract = monthMap[period];
    startDate = moment().startOf("month").subtract(monthsToSubtract, "months");
    endDate = today;
    prevStartDate = moment(startDate).subtract(monthsToSubtract + 1, "months");
    prevEndDate = moment(startDate).subtract(1, "day");
  } else {
    startDate = moment().startOf("month");
    endDate = today;
    prevStartDate = moment(startDate).subtract(1, "month");
    prevEndDate = moment(startDate).subtract(1, "day");
  }

  return { startDate, endDate, prevStartDate, prevEndDate };
};

const getUserAnalyticsData = async (req, res) => {
  try {
    const { period = "currentMonth" } = req.body;
    const { startDate, endDate, prevStartDate, prevEndDate } =
      getMonthPeriod(period);

    const roleFilter = { role: 2, is_deleted: false };

    // Total Users
    const [currentUsers, previousCurrentUsers] = await Promise.all([
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [startDate, endDate] },
        },
      }),
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [prevStartDate, prevEndDate] },
        },
      }),
    ]);

    const currentUserChange = getPercentageChange(
      currentUsers,
      previousCurrentUsers,
    );

    // Active Users (logged in during period)
    const [activeUsers, previousActiveUsers] = await Promise.all([
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [startDate, endDate] },
          status: "active",
        },
      }),
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [prevStartDate, prevEndDate] },
          status: "active",
        },
      }),
    ]);

    const activeUserChange = getPercentageChange(
      activeUsers,
      previousActiveUsers,
    );

    // geography wise split
    const geographySplit = await User.findAll({
      attributes: ["country", [fn("COUNT", col("id")), "count"]],
      where: {
        ...roleFilter,
        created_at: { [Op.between]: [startDate, endDate] },
      },
      group: ["country"],
      order: [[literal("count"), "DESC"]],
      limit: 10,
      raw: true,
    });

    // top locations wise split
    const locationSplit = await User.findAll({
      attributes: ["location", [fn("COUNT", col("id")), "count"]],
      where: {
        ...roleFilter,
        location: { [Op.ne]: null },
        created_at: { [Op.between]: [startDate, endDate] },
      },
      group: ["location"],
      order: [[literal("count"), "DESC"]],
      limit: 10,
      raw: true,
    });

    // Top users (based on posts)
    const topUsers = await Post.findAll({
      attributes: ["user_id", [fn("COUNT", col("Post.id")), "post_count"]],
      where: { created_at: { [Op.between]: [startDate, endDate] } },
      group: ["user_id"],
      order: [[literal("post_count"), "DESC"]],
      limit: 10,
      include: [
        {
          model: User,
          as: "user",
          attributes: ["username", "profile_picture_url", "country"],
        },
      ],
      raw: true,
    });

    // gender wise split
    const genderSplit = await User.findAll({
      attributes: ["gender", [fn("COUNT", col("id")), "count"]],
      where: {
        ...roleFilter,
        gender: { [Op.ne]: null },
        created_at: { [Op.between]: [startDate, endDate] },
      },
      group: ["gender"],
      order: [[literal("count"), "DESC"]],
      raw: true,
    });

    const analytics = {
      timeSpent: {
        count: 0,
        percentageChange: 0,
      },
      totalUsers: {
        count: currentUsers,
        percentageChange: currentUserChange,
      },
      activeUsers: {
        count: activeUsers,
        percentageChange: activeUserChange,
      },
      geographySplit,
      locationSplit,
      topUsers,
      genderSplit,
      totalInstalled: 0,
      totalDownloads: 0,
      currentDownloaded: 0,
      areaInstalled: 0,
    };

    console.log(analytics);

    sendSuccess(res, "User analytics retrieved successfully", 200, analytics);
  } catch (error) {
    sendError(
      res,
      error?.message || "Failed to get user analytics",
      500,
      error,
    );
  }
};

module.exports = {
  getLiveMetricsData,
  getUserAnalyticsData,
};
