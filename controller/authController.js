const crypto = require("crypto");
const bcrypt = require("bcryptjs");
const { Op } = require("sequelize");
const { User, Role, Token } = require("../models");
const { sendError, sendSuccess } = require("../jsonResponse");
const { signAccessToken, signRefreshToken } = require("../utils/jwt");
const { sendmail } = require("../utils/email");

const login = async (req, res) => {
  const { email, password } = req.body;

  try {
    const user = await User.findOne({
      where: {
        is_deleted: false,
        [Op.or]: [{ email: email }, { username: email }],
      },
    });

    if (!user || !(await bcrypt.compare(password, user.password_hash))) {
      return sendError(res, "Invalid credentials", 403);
    }

    const accessToken = signAccessToken(user);
    const refreshToken = signRefreshToken(user);

    await user.update({ last_login_at: new Date() });

    sendSuccess(res, "Login successful", 200, {
      accessToken,
      refreshToken,
      user,
    });
  } catch (error) {
    sendError(res, error?.message || "Login Failed", 500, error);
  }
};

const forgotPassword = async (req, res) => {
  const { email } = req.body;
  try {
    const user = await User.findOne({
      where: {
        is_deleted: false,
        email,
      },
    });

    if (!user) return sendError(res, "User not found", 403);

    const resetToken = crypto.randomBytes(32).toString("hex");
    const expires = new Date(Date.now() + 10 * 60 * 1000); // Token expires in 1o minutes

    await Token.create({
      email: email,
      user_id: user.id,
      token: resetToken,
      expires,
      type: "password_reset",
    });
    const resetLink = `${process.env.FRONTEND_URL}/auth/reset-password?token=${resetToken}`;

    const data = {
      to: email,
      subject: "Password Reset Request",
      template: "forgot-password.ejs",
      content: {
        name: user?.name,
        resetPasswordUrl: resetLink,
      },
    };
    const emailResponse = await sendmail(data);

    if (emailResponse.success) {
      sendSuccess(res, "Password reset link sent to your email", 200, {});
    } else {
      sendError(res, "Failed to send email", 500);
    }
  } catch (error) {
    sendError(res, error?.message || "Login Failed", 500, error);
  }
};

const resetPassword = async (req, res) => {
  const { token, password } = req.body;

  try {
    if (!token) return sendError(res, "Invalid or expired reset link", 403);

    const resetToken = await Token.findOne({ where: { token } });

    if (!resetToken || resetToken.expires < Date.now()) {
      return sendError(
        res,
        "Reset link has expired. Please request a new one",
        403,
      );
    }

    const user = await User.findOne({ where: { id: resetToken.user_id } });

    if (!user) return sendError(res, "User not found", 404);

    user.password_hash = password;
    await user.save();
    await resetToken.destroy();

    sendSuccess(res, "Password reset successfully", 200, {});
  } catch (error) {
    sendError(res, error?.message || "Failed to reset password", 500, error);
  }
};

const getUserData = async (req, res) => {
  const { id } = req.user;
  try {
    const user = await User.findOne({
      where: { id },
      include: [
        {
          model: Role,
          as: "userRole",
          attributes: ["role", "roleDisplayName"],
        },
      ],
    });

    if (!user) {
      return sendError(res, "user not found", 404);
    }

    sendSuccess(res, "Fetch user data successful", 200, user);
  } catch (error) {
    sendError(res, error?.message || "Failed to fetch user data", 500, error);
  }
};

module.exports = {
  login,
  forgotPassword,
  resetPassword,
  getUserData,
};
