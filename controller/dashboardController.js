const { Op, fn, col, literal } = require("sequelize");
const { Post, User, Like, Comment } = require("../models");
const { sendError, sendSuccess } = require("../jsonResponse");

const getPercentageChange = (current, previous) => {
  if (previous === 0) return current === 0 ? 0 : 100;
  return ((current - previous) / previous) * 100;
};

const getDashboardData = async (req, res) => {
  try {
    const now = new Date();

    // Current month range
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const currentMonthEnd = new Date(
      now.getFullYear(),
      now.getMonth() + 1,
      0,
      23,
      59,
      59,
    );

    // Previous month range
    const previousMonthStart = new Date(
      now.getFullYear(),
      now.getMonth() - 1,
      1,
    );
    const previousMonthEnd = new Date(
      now.getFullYear(),
      now.getMonth(),
      0,
      23,
      59,
      59,
    );

    const roleFilter = { role: 2 };

    const [currentUserCount, previousUserCount] = await Promise.all([
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [currentMonthStart, currentMonthEnd] },
        },
      }),
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [previousMonthStart, previousMonthEnd] },
        },
      }),
    ]);

    const [activeUserCount, previousActiveUserCount] = await Promise.all([
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [currentMonthStart, currentMonthEnd] },
          status: "active",
        },
      }),
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [previousMonthStart, previousMonthEnd] },
          status: "active",
        },
      }),
    ]);

    const [inactiveUserCount, previousInactiveUserCount] = await Promise.all([
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [currentMonthStart, currentMonthEnd] },
          status: "inactive",
        },
      }),
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [previousMonthStart, previousMonthEnd] },
          status: "inactive",
        },
      }),
    ]);

    const [newInstalls, previousInstalls] = await Promise.all([
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [currentMonthStart, currentMonthEnd] },
        },
      }),
      User.count({
        where: {
          ...roleFilter,
          created_at: { [Op.between]: [previousMonthStart, previousMonthEnd] },
        },
      }),
    ]);

    const [uninstalls, previousUninstalls] = await Promise.all([
      User.count({
        where: {
          ...roleFilter,
          is_deleted: true,
          deleted_at: { [Op.between]: [currentMonthStart, currentMonthEnd] },
        },
      }),
      User.count({
        where: {
          ...roleFilter,
          is_deleted: true,
          deleted_at: { [Op.between]: [previousMonthStart, previousMonthEnd] },
        },
      }),
    ]);

    const userChange = getPercentageChange(currentUserCount, previousUserCount);
    const activeUserChange = getPercentageChange(
      activeUserCount,
      previousActiveUserCount,
    );
    const inactiveUserChange = getPercentageChange(
      inactiveUserCount,
      previousInactiveUserCount,
    );
    const installChange = getPercentageChange(newInstalls, previousInstalls);
    const uninstallChange = getPercentageChange(uninstalls, previousUninstalls);

    sendSuccess(res, "dashboard data retrieved successfully", 200, {
      users: {
        count: currentUserCount,
        percentageChange: userChange,
      },
      activeUsers: {
        count: activeUserCount,
        percentageChange: activeUserChange,
      },
      inactiveUsers: {
        count: inactiveUserCount,
        percentageChange: inactiveUserChange,
      },
      newInstalls: {
        count: newInstalls,
        percentageChange: installChange,
      },
      uninstalls: {
        count: uninstalls,
        percentageChange: uninstallChange,
      },
      goLive: {
        totalSessions: 120,
        ongoingLives: 5,
        avergaeViewDurations: "24 min",
        peakViewers: 240,
      },
      donations: {
        totalDonations: 18500,
        totalDonors: 350,
        averageDonations: 350,
        lineChartData: [
          { month: "2024-12", amount: 8200 },
          { month: "2025-01", amount: 10300 },
          { month: "2025-02", amount: 9500 },
          { month: "2025-03", amount: 12000 },
          { month: "2025-04", amount: 14300 },
          { month: "2025-05", amount: 18500 },
        ],
        topCampaigns: [
          {
            name: "Clean Water for All",
            amountRaised: 5200,
            donors: 140,
            goalPercentage: 87,
          },
          {
            name: "Education First",
            amountRaised: 4300,
            donors: 120,
            goalPercentage: 72,
          },
          {
            name: "Help Gaza Children",
            amountRaised: 3600,
            donors: 90,
            goalPercentage: 45,
          },
          {
            name: "Women Empowerment Fund",
            amountRaised: 3200,
            donors: 85,
            goalPercentage: 64,
          },
          {
            name: "Climate Relief Drive",
            amountRaised: 2200,
            donors: 60,
            goalPercentage: 55,
          },
        ],
      },
    });
  } catch (error) {
    sendError(
      res,
      error?.message || "Failed to get dashboard data",
      500,
      error,
    );
  }
};

const getDashboardPostData = async (req, res) => {
  try {
    const today = new Date();
    const past7Days = new Date();
    past7Days.setDate(today.getDate() - 6);

    const formatDate = (date) => date.toISOString().split("T")[0];

    const dateArray = [];
    for (let i = 6; i >= 0; i--) {
      const d = new Date(today);
      d.setDate(today.getDate() - i);
      dateArray.push(formatDate(d));
    }

    const postCounts = await Post.findAll({
      where: {
        is_deleted: false,
        created_at: {
          [Op.between]: [past7Days, today],
        },
      },
      attributes: [
        [fn("DATE", col("created_at")), "date"],
        [fn("COUNT", "*"), "posts"],
      ],
      group: [fn("DATE", col("created_at"))],
      raw: true,
    });

    const postMap = Object.fromEntries(
      postCounts.map((item) => [item.date, parseInt(item.posts)]),
    );

    const postsArray = dateArray.map((date) => ({
      date,
      posts: postMap[date] || 0,
    }));

    const storiesArray = dateArray.map((date) => ({
      date,
      stories: 0,
    }));

    const topPosts = await Post.findAll({
      where: {
        is_deleted: false,
        media_url: {
          [Op.ne]: [],
        },
      },
      include: [
        {
          model: Like,
          as: "likes",
          attributes: [],
        },
        {
          model: Comment,
          as: "comments",
          attributes: [],
        },
      ],
      attributes: [
        "id",
        "content",
        "media_url",
        [fn("COUNT", col("likes.id")), "likeCount"],
        [fn("COUNT", col("comments.id")), "commentCount"],
      ],
      group: ["Post.id"],
      order: [[literal("COUNT(likes.id) + COUNT(comments.id)"), "DESC"]],
      limit: 5,
      subQuery: false,
    });

    sendSuccess(res, "dashboard data retrieved successfully", 200, {
      postsArray,
      storiesArray,
      topPosts,
      goLive: {
        totalSessions: 120,
        ongoingLives: 5,
        avergaeViewDurations: "24 min",
        peakViewers: 240,
      },
      donations: {
        totalDonations: 18500,
        totalDonors: 350,
        averageDonations: 350,
        lineChartData: [
          { month: "2024-12", amount: 8200 },
          { month: "2025-01", amount: 10300 },
          { month: "2025-02", amount: 9500 },
          { month: "2025-03", amount: 12000 },
          { month: "2025-04", amount: 14300 },
          { month: "2025-05", amount: 18500 },
        ],
        topCampaigns: [
          {
            name: "Clean Water for All",
            amountRaised: 5200,
            donors: 140,
            goalPercentage: 87,
          },
          {
            name: "Education First",
            amountRaised: 4300,
            donors: 120,
            goalPercentage: 72,
          },
          {
            name: "Help Gaza Children",
            amountRaised: 3600,
            donors: 90,
            goalPercentage: 45,
          },
          {
            name: "Women Empowerment Fund",
            amountRaised: 3200,
            donors: 85,
            goalPercentage: 64,
          },
          {
            name: "Climate Relief Drive",
            amountRaised: 2200,
            donors: 60,
            goalPercentage: 55,
          },
        ],
      },
    });
  } catch (error) {
    sendError(
      res,
      error?.message || "Failed to get dashboard post data",
      500,
      error,
    );
  }
};

module.exports = {
  getDashboardData,
  getDashboardPostData,
};
