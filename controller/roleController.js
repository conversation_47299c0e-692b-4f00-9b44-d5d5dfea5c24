const { Role, Permission, RolePermission } = require("../models");
const { sendError, sendSuccess } = require("../jsonResponse");

const getRoleList = async (req, res) => {
  try {
    const roles = await Role.findAll({
      raw: true,
    });

    sendSuccess(res, "Roles retrieved successfully", 200, {
      roles,
    });
  } catch (error) {
    sendError(res, error?.message || "Failed to get roles list", 500, error);
  }
};

const getPermissionList = async (req, res) => {
  try {
    const permissions = await Permission.findAll({
      raw: true,
    });

    sendSuccess(res, "Permissions retrieved successfully", 200, {
      permissions,
    });
  } catch (error) {
    sendError(res, error?.message || "Failed to get permission list", 500, error);
  }
};

const getRolePermissionList = async (req, res) => {
  try {
    const roles = await RolePermission.findAll({
      include: [
        {
          model: Role,
          attributes: ["id", "role", "roleDisplayName"],
        },
        {
          model: Permission,
          attributes: ["id", "permission", "displayName"],
        },
      ],

      raw: true,
    });

    sendSuccess(res, "Role permissions retrieved successfully", 200, {
      roles,
    });
  } catch (error) {
    sendError(
      res,
      error?.message || "Failed to get roles permissions",
      500,
      error,
    );
  }
};

const updateRolePermissionList = async (req, res) => {
  try {
    } catch (error) {
    sendError(
      res,
      error?.message || "Failed to update roles permissions",
      500,
      error,
    );
  }
};

module.exports = {
  getRoleList,
  getPermissionList,
  getRolePermissionList,
  updateRolePermissionList,
};
