const { User, Role } = require("../models");
const { sendError, sendSuccess } = require("../jsonResponse");
const { userSearchFields } = require("../utils/constant");
const { buildMatchQuery, getPaginationMetaData } = require("./helper");
const { Op } = require("sequelize");

const getUserList = async (req, res) => {
  try {
    const { whereClause, orderClause, skip, limit } = buildMatchQuery(
      req,
      {},
      userSearchFields,
    );

    const { rows: users, count: totalCount } = await User.findAndCountAll({
      where: whereClause,
      order: Object.entries(orderClause),
      offset: skip,
      limit: limit,
      include: [
        {
          model: Role,
          as: "userRole",
          attributes: ["role", "roleDisplayName"],
          where: {
            role: { [Op.ne]: "user" }, // For role NOT "user"
          },
        },
      ],
      raw: true,
    });

    const metadata = getPaginationMetaData(totalCount, skip, limit);

    sendSuccess(res, "Users retrieved successfully", 200, {
      users,
      metadata,
    });
  } catch (error) {
    sendError(res, error?.message || "Failed to get user list", 500, error);
  }
};

const getUserDetail = async (req, res) => {
  const { userId } = req.body;
  try {
    const user = await User.findOne({
      where: { id: userId },
      include: [
        {
          model: Role,
          as: "userRole",
          attributes: ["role", "roleDisplayName"],
        },
      ],
    });

    if (!user) {
      return sendError(res, "user not found", 404);
    }

    sendSuccess(res, "Fetch user data successful", 200, user);
  } catch (error) {
    sendError(res, error?.message || "Failed to fetch user data", 500, error);
  }
};

const createUser = async (req, res) => {
  const {
    first_name,
    last_name,
    email,
    mobile_number,
    password,
    role,
    accountStatus,
  } = req.body;
  try {
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return sendError(res, "Email is already in use", 400);
    }

    const randomNumber = Math.floor(10 + Math.random() * 90);
    const username = `${first_name.toLowerCase()}${last_name.toLowerCase()}${randomNumber}`;

    const newUser = await User.create({
      username,
      first_name,
      last_name,
      email,
      mobile_number,
      password_hash: password,
      role,
      status: accountStatus,
    });

    sendSuccess(res, "User created successfully", 201, { user: newUser });
  } catch (error) {
    sendError(res, error?.message || "Failed to add user", 500, error);
  }
};

const editUser = async (req, res) => {
  const {
    userId,
    first_name,
    last_name,
    email,
    mobile_number,
    password,
    role,
    accountStatus,
  } = req.body;
  try {
    const user = await User.findOne({ where: { id: userId } });

    if (!user) {
      return sendError(res, "User not found", 404);
    }

    user.first_name = first_name;
    user.last_name = last_name;
    user.email = email;
    user.mobile_number = mobile_number;
    user.role = role;
    user.status = accountStatus;

    if (password) {
      user.password_hash = password;
    }

    await user.save();

    sendSuccess(res, "User Updated successfully", 200, { user });
  } catch (error) {
    sendError(res, error?.message || "Failed to edit user", 500, error);
  }
};

const deleteUser = async (req, res) => {
  const { userId } = req.body;
  try {
    const user = await User.findByPk(userId);
    if (!user) {
      return sendError(res, "User not found", 404);
    }

    user.status = "Inactive";
    user.deleted_at = new Date();

    await user.save();

    sendSuccess(res, "User has been marked as inactive", 200, { user });
  } catch (error) {
    sendError(res, error?.message || "Failed to delete user", 500, error);
  }
};

const changePassword = async (req, res) => {
  const { oldPassword, newPassword, confirmNewPassword } = req.body;
  const { id } = req.user;

  try {
    if (newPassword !== confirmNewPassword) {
      return sendError(res, "Passwords do not match", 400);
    }

    const user = await User.findOne({ where: { id } });
    if (!user) return sendError(res, "User not found", 404);

    const isMatch = await user.isPasswordCorrect(oldPassword);
    if (!isMatch) return sendError(res, "Incorrect old password", 403);

    const isSamePassword = await user.isPasswordCorrect(newPassword);
    if (isSamePassword)
      return sendError(
        res,
        "New password must be different from the current password",
        400,
      );

    user.password_hash = newPassword;
    await user.save();

    sendSuccess(res, "Password changed successfully", 200, {});
  } catch (error) {
    sendError(res, error?.message || "Failed to change password", 500, error);
  }
};

const updateProfile = async (req, res) => {
  const { first_name, last_name, email, role } = req.body;
  const { id } = req.user;
  try {
    const user = await User.findOne({ where: { id } });
    if (!user) {
      return sendError(res, "User not found", 404);
    }

    user.first_name = first_name;
    user.last_name = last_name;
    user.email = email;
    user.role = role;

    await user.save();

    sendSuccess(res, "Profile Updated successfully", 200, {});
  } catch (error) {
    sendError(res, error?.message || "Failed to update profile", 500, error);
  }
};

module.exports = {
  getUserDetail,
  getUserList,
  createUser,
  editUser,
  deleteUser,
  changePassword,
  updateProfile,
};
