const { Ad } = require("../models");
const { sendError, sendSuccess } = require("../jsonResponse");
const { adsSearchFields } = require("../utils/constant");
const { buildMatchQuery, getPaginationMetaData } = require("./helper");

const getAdsList = async (req, res) => {
  try {
    const { whereClause, orderClause, skip, limit } = buildMatchQuery(
      req,
      {},
      adsSearchFields,
    );

    const { rows: ads, count: totalCount } = await Ad.findAndCountAll({
      where: whereClause,
      order: Object.entries(orderClause),
      offset: skip,
      limit: limit,
      raw: true,
    });

    const metadata = getPaginationMetaData(totalCount, skip, limit);

    sendSuccess(res, "Ads retrieved successfully", 200, {
      ads,
      metadata,
    });
  } catch (error) {
    sendError(res, error?.message || "Failed to get ads list", 500, error);
  }
};

const getAdDetail = async (req, res) => {
  const { id } = req.body;
  try {
    const ads = await Ad.findOne({
      where: { id },
    });

    if (!ads) {
      return sendError(res, "ads not found", 404);
    }

    sendSuccess(res, "Fetch ads data successful", 200, ads);
  } catch (error) {
    sendError(res, error?.message || "Failed to fetch ads data", 500, error);
  }
};

const createAd = async (req, res) => {
  const {
    ad_title,
    advertiser_name,
    ad_type,
    ad_placement,
    category,
    target_audience,
    start_date,
    end_date,
    status,
    ad_image,
    ad_media,
  } = req.body;
  try {
    if (!ad_image || !ad_media) {
      return sendError(res, "Ad image and media are required", 400);
    }

    const newAd = await Ad.create({
      ad_title,
      advertiser_name,
      ad_type,
      ad_image,
      ad_placement,
      category,
      target_audience,
      ad_media,
      start_date,
      end_date,
      status,
      clicks: 0,
    });

    sendSuccess(res, "Ads created successfully", 201, {
      ads: newAd,
    });
  } catch (error) {
    sendError(res, error?.message || "Failed to add Ads", 500, error);
  }
};

const editAd = async (req, res) => {
  const {
    id,
    ad_title,
    advertiser_name,
    ad_type,
    ad_placement,
    category,
    target_audience,
    start_date,
    end_date,
    status,
    ad_image,
    ad_media,
  } = req.body;
  try {
    const ads = await User.findOne({ where: { id } });

    if (!ads) {
      return sendError(res, "Ads not found", 404);
    }

    // Update ad fields
    ads.ad_title = ad_title;
    ads.advertiser_name = advertiser_name;
    ads.ad_type = ad_type;
    ads.ad_placement = ad_placement;
    ads.category = category;
    ads.target_audience = target_audience;
    ads.start_date = start_date;
    ads.end_date = end_date;
    ads.status = status;
    if (ad_image) ads.ad_image = ad_image;
    if (ad_media) ads.ad_media = ad_media;

    await ads.save();

    sendSuccess(res, "Ads Updated successfully", 200, { ads });
  } catch (error) {
    sendError(res, error?.message || "Failed to edit ads", 500, error);
  }
};

const deleteAd = async (req, res) => {
  const { id } = req.body;
  try {
    const ads = await Ad.findByPk(id);
    if (!ads) {
      return sendError(res, "Ads not found", 404);
    }

    ads.status = "Inactive";
    ads.deleted_at = new Date();

    await ads.save();

    sendSuccess(res, "Ads has been marked as inactive", 200, { ads });
  } catch (error) {
    sendError(res, error?.message || "Failed to delete ads", 500, error);
  }
};

module.exports = {
  getAdsList,
  getAdDetail,
  createAd,
  editAd,
  deleteAd,
};
