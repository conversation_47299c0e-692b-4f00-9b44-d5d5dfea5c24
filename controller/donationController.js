const { sendError, sendSuccess } = require("../jsonResponse");

const data = {
  summary: {
    totalGoal: 65500,
    totalRaised: 54000,
    totalPending: 11500,
  },
  donationEvents: [
    {
      eventId: "EVT001",
      title: "School Library Drive",
      organizer: "<PERSON> Johnson",
      goal: 10000,
      raised: 6500,
      pending: 3500,
      startDate: "2025-04-01",
      endDate: "2025-06-01",
      status: "Ongoing",
    },
    {
      eventId: "EVT002",
      title: "Clean Water Project",
      organizer: "Green Foundation",
      goal: 15000,
      raised: 15000,
      pending: 0,
      startDate: "2025-01-10",
      endDate: "2025-04-30",
      status: "Completed",
    },
    {
      eventId: "EVT003",
      title: "Community Health Camp",
      organizer: "Dr. Lee Clinic",
      goal: 8000,
      raised: 5000,
      pending: 3000,
      startDate: "2025-03-05",
      endDate: "2025-05-20",
      status: "Ongoing",
    },
    {
      eventId: "EVT004",
      title: "Animal Shelter Expansion",
      organizer: "PetKind Org",
      goal: 12500,
      raised: 8200,
      pending: 4300,
      startDate: "2025-02-15",
      endDate: "2025-05-30",
      status: "Ongoing",
    },
    {
      eventId: "EVT005",
      title: "Youth Coding Bootcamp",
      organizer: "Code4Change",
      goal: 20000,
      raised: 19300,
      pending: 700,
      startDate: "2025-01-01",
      endDate: "2025-03-31",
      status: "Completed",
    },
  ],
};

const detail = {
  eventId: "EVT001",
  title: "School Library Drive",
  organizer: "Alice Johnson",
  goal: 10000,
  raised: 6500,
  pending: 3500,
  startDate: "2025-04-01",
  endDate: "2025-06-01",
  status: "Ongoing",
  donations: [
    {
      donorName: "John Doe",
      donorEmail: "<EMAIL>",
      amount: 1500,
      paymentMethod: "Credit Card",
      status: "Success",
      date: "2025-04-05T10:15:00Z",
    },
    {
      donorName: "Emily Carter",
      donorEmail: "<EMAIL>",
      amount: 1000,
      paymentMethod: "PayPal",
      status: "Success",
      date: "2025-04-07T12:30:00Z",
    },
    {
      donorName: "Michael Smith",
      donorEmail: "<EMAIL>",
      amount: 2000,
      paymentMethod: "UPI",
      status: "Pending",
      date: "2025-04-10T09:45:00Z",
    },
    {
      donorName: "Anonymous",
      donorEmail: "N/A",
      amount: 2000,
      paymentMethod: "Bank Transfer",
      status: "Success",
      date: "2025-04-12T14:00:00Z",
    },
  ],
};

const getDonationData = async (req, res) => {
  try {
    sendSuccess(res, "donation data retrieved successfully", 200, {
      data,
    });
  } catch (error) {
    sendError(res, error?.message || "Failed to get donation data", 500, error);
  }
};

const updateDonation = async (req, res) => {
  try {
    if (!req.user || req.user.roles !== 1) {
      return sendError(res, "Access denied: Administrator only", 403);
    }

    const { eventId, title, goal, startDate, endDate } = req.body;

    sendSuccess(res, "donation data updated successfully", 200, {});
  } catch (error) {
    sendError(
      res,
      error?.message || "Failed to update donation data",
      500,
      error,
    );
  }
};

const donationDetail = async (req, res) => {
  try {
    const { eventId } = req.body;

    sendSuccess(res, "donation detail retrieved successfully", 200, {
      detail,
    });
  } catch (error) {
    sendError(
      res,
      error?.message || "Failed to get donation detail",
      500,
      error,
    );
  }
};

module.exports = {
  getDonationData,
  updateDonation,
  donationDetail,
};
