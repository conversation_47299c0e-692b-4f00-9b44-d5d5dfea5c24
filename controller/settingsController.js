const { sendError, sendSuccess } = require("../jsonResponse");

const getSettingsData = async (req, res) => {
  try {
    sendSuccess(res, "settings data retrieved successfully", 200, {});
  } catch (error) {
    sendError(res, error?.message || "Failed to get settings data", 500, error);
  }
};
const updateSettings = async (req, res) => {
  try {
    sendSuccess(res, "settings data updated successfully", 200, {});
  } catch (error) {
    sendError(
      res,
      error?.message || "Failed to update settings data",
      500,
      error,
    );
  }
};

module.exports = {
  getSettingsData,
  updateSettings,
};
