const { Op } = require("sequelize");
const { Ad, User } = require("../models");
const { sendError, sendSuccess } = require("../jsonResponse");

const downloadReportData = async (req, res) => {
  try {
    const { reportType, exportType, startDate, endDate } = req.body;

    if (!reportType) return sendError(res, "Report Type is required", 400);
    if (!exportType) return sendError(res, "Export Type is required", 400);

    let data = [];
    const where = {};

    if (startDate && endDate) {
      const dateField = reportType === "ad" ? "createdAt" : "created_at";
      where[dateField] = {
        [Op.between]: [startDate, endDate],
      };
    } else if ((startDate && !endDate) || (!startDate && endDate)) {
      return sendError(
        res,
        "Start Date and End Date are required if date range filtering is chosen",
        400,
      );
    }

    if (reportType === "ad") {
      data = await Ad.findAll({ where, raw: true });
    } else if (reportType === "user") {
      where.role = 2;
      data = await User.findAll({ where, raw: true });
    } else {
      return sendError(res, "Invalid Report Type", 400);
    }

    sendSuccess(res, "report retrieved successfully", 200, {
      data,
    });
  } catch (error) {
    sendError(res, error?.message || "Failed to get report", 500, error);
  }
};

module.exports = {
  downloadReportData,
};
