const { S3Client, PutObjectCommand } = require("@aws-sdk/client-s3");
const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
const { v4: uuidv4 } = require("uuid");
const { sendError, sendSuccess } = require("../jsonResponse");

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

const generatePresignedUrl = async (req, res) => {
  const { filename, type: fileType } = req.body;
  const fileExtension = filename.split(".").pop();

  const allowedImageTypes = ["image/jpeg", "image/png", "image/gif"];
  const allowedVideoTypes = ["video/mp4"];

  if (
    ![...allowedImageTypes, ...allowedVideoTypes].includes(fileType) ||
    !["jpg", "jpeg", "png", "gif", "mp4"].includes(fileExtension)
  ) {
    return res.status(400).json({ error: "Invalid file type or extension." });
  }

  const key = `uploads/admin/${uuidv4()}.${fileExtension}`;

  const command = new PutObjectCommand({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: key,
    ContentType: fileType,
    // ACL: 'public-read',
  });

  try {
    const presignedUrl = await getSignedUrl(s3Client, command, {
      expiresIn: 300,
    });
    const fileUrl = `https://d2p8o1syjdr4y0.cloudfront.net/${key}`;

    sendSuccess(res, "Created presigned url", 200, {
      presignedUrl,
      fileUrl,
      key,
    });
  } catch (error) {
    sendError(
      res,
      error?.message || "Failed to create presigned url",
      500,
      error,
    );
  }
};

module.exports = {
  generatePresignedUrl,
};
