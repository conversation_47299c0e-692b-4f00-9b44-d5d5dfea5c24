const buildMatchQuery = (req, matchQuery = {}, fields = []) => {
  matchQuery = null;

  const body = req.body || {};

  // filter
  if (body.filter) {
    const filter = body.filter;

    Object.keys(filter).forEach((key) => {
      const value = filter[key];

      if (value.fromDate && value.toDate) {
        const fromDate = moment(value.fromDate).toDate();
        const toDate = moment(value.toDate).toDate();
        matchQuery[key] = { lt: toDate, gt: fromDate };
      } else if (value.fromDate) {
        const fromDate = moment(value.fromDate).toDate();
        matchQuery[key] = { gt: fromDate };
      } else if (value.toDate) {
        const toDate = moment(value.toDate).toDate();
        matchQuery[key] = { lt: toDate };
      } else if (value.from && value.to) {
        matchQuery[key] = { lt: value.to, gt: value.from };
      } else if (value.from) {
        matchQuery[key] = { gt: value.from };
      } else if (value.to) {
        matchQuery[key] = { lt: value.to };
      } else if (value.reg) {
        const regExp = new RegExp(value.reg, "i");
        matchQuery[key] = { contains: regExp.source, mode: "insensitive" };
      } else {
        matchQuery[key] = value;
      }
    });
  }

  // search
  if (body.search) {
    matchQuery["OR"] = fields.map((field) => ({
      [field]: { contains: body.search, mode: "insensitive" },
    }));
  }

  // sort
  let orderBy = { id: "desc" };
  if (body.sortBy & body.sortOrder) {
    orderBy = {
      [body.sortBy]: body.sortOrder.toLowerCase() === "asc" ? "asc" : "desc",
    };
  }

  // pagination
  const page = parseInt(body.page) || 1;
  const limit = parseInt(body.limit) || 10;
  const skip = (page - 1) * limit;

  return { whereClause: matchQuery, orderClause: orderBy, skip, limit };
};

const getPaginationMetaData = (totalCount, skip, take) => {
  const totalPages = Math.ceil(totalCount / take);
  const currentPage = Math.floor(skip / take) + 1;
  const nextPage = currentPage < totalPages ? currentPage + 1 : null;
  const prevPage = currentPage > 1 ? currentPage - 1 : null;

  return {
    totalCount,
    totalPages,
    currentPage,
    nextPage,
    prevPage,
    pageSize: take,
  };
};

module.exports = {
  buildMatchQuery,
  getPaginationMetaData,
};
