const { Cms } = require("../models");
const { sendError, sendSuccess } = require("../jsonResponse");

// Get all CMS pages
const getCMSPages = async (req, res) => {
  try {
    const pages = await Cms.findAll({});

    sendSuccess(res, "CMS pages retrieved successfully", 200, { cms: pages });
  } catch (error) {
    sendError(res, error?.message || "Failed to get CMS pages", 500, error);
  }
};

// Get CMS page by id
const getCMSById = async (req, res) => {
  const { cmsId } = req.body;
  try {
    const page = await Cms.findOne({
      where: { id: cmsId },
    });

    if (!page) {
      return sendError(res, "Page not found", 404);
    }

    sendSuccess(res, "CMS page retrieved successfully", 200, page);
  } catch (error) {
    sendError(res, error?.message || "Failed to get CMS page", 500, error);
  }
};
// Get CMS page by slug
const getCMSPageBySlug = async (req, res) => {
  const { slug } = req.body;
  try {
    const page = await Cms.findOne({
      where: { slug, status: "active" },
    });

    if (!page) {
      return sendError(res, "Page not found", 404);
    }

    sendSuccess(res, "CMS page retrieved successfully", 200, { page });
  } catch (error) {
    sendError(res, error?.message || "Failed to get CMS page", 500, error);
  }
};

// Admin routes with authentication
const createCMSPage = async (req, res) => {
  const { title, content, status } = req.body;
  try {
    // Create slug from title
    const slug = title.toLowerCase().replace(/\s+/g, "-");

    const existingPage = await Cms.findOne({ where: { slug } });
    if (existingPage) {
      return sendError(res, "A page with this title already exists", 400);
    }

    const page = await Cms.create({
      title,
      slug,
      content,
      status: status || "active",
    });

    sendSuccess(res, "CMS page created successfully", 201, { page });
  } catch (error) {
    sendError(res, error?.message || "Failed to create CMS page", 500, error);
  }
};

const updateCMSPage = async (req, res) => {
  const { id, title, content, status } = req.body;
  try {
    const page = await Cms.findByPk(id);

    if (!page) {
      return sendError(res, "Page not found", 404);
    }

    // Update slug only if title changes
    let slug = page.slug;
    if (title && title !== page.title) {
      slug = title.toLowerCase().replace(/\s+/g, "-");
    }

    await page.update({
      title: title || page.title,
      slug,
      content: content || page.content,
      status: status || page.status,
    });

    sendSuccess(res, "CMS page updated successfully", 200, { page });
  } catch (error) {
    sendError(res, error?.message || "Failed to update CMS page", 500, error);
  }
};

const deleteCMSPage = async (req, res) => {
  const { id } = req.body;
  try {
    const page = await Cms.findByPk(id);

    if (!page) {
      return sendError(res, "Page not found", 404);
    }

    await page.destroy();

    sendSuccess(res, "CMS page deleted successfully", 200);
  } catch (error) {
    sendError(res, error?.message || "Failed to delete CMS page", 500, error);
  }
};

module.exports = {
  getCMSPages,
  getCMSById,
  getCMSPageBySlug,
  createCMSPage,
  updateCMSPage,
  deleteCMSPage,
};
