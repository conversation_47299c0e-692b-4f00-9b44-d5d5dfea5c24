const jwt = require("jsonwebtoken");
const fs = require("fs");
const path = require("path");

const accessTokenExpiry = process.env.JWT_ACCESS_TOKEN_EXPIRY;
const refreshTokenExpiry = process.env.JWT_ACCESS_TOKEN_EXPIRY;

const publicKey = fs.readFileSync(
  `${path.join(__dirname, "../certificates")}/public.pem`,
  "utf8",
);

const privateKey = fs.readFileSync(
  `${path.join(__dirname, "../certificates")}/private.pem`,
  "utf8",
);

const signAccessToken = (user) => {
  return jwt.sign(
    {
      id: user.id,
      roles: user.role,
    },
    privateKey,
    {
      algorithm: "RS256",
      expiresIn: accessTokenExpiry,
    },
  );
};

const signRefreshToken = (user) => {
  return jwt.sign(
    {
      id: user.id,
      roles: user.role,
    },
    privateKey,
    {
      algorithm: "RS256",
      expiresIn: refreshTokenExpiry,
    },
  );
};

const verifyAccessToken = (token) => {
  try {
    return jwt.verify(token, publicKey);
  } catch (err) {
    return null;
  }
};

const verifyRefreshToken = (token) => {
  try {
    return jwt.verify(token, publicKey);
  } catch (err) {
    return null;
  }
};

module.exports = {
  signAccessToken,
  signRefreshToken,
  verifyAccessToken,
  verifyRefreshToken,
};
