const { subHours, subDays, startOfHour, startOfDay } = require("date-fns");

const getDateRanges = (range) => {
  const now = new Date();
  switch (range) {
    case "1h":
      return {
        currentStart: subHours(now, 1),
        prevStart: subHours(now, 2),
        prevEnd: subHours(now, 1),
      };
    case "24h":
      return {
        currentStart: subDays(now, 1),
        prevStart: subDays(now, 2),
        prevEnd: subDays(now, 1),
      };
    case "7d":
      return {
        currentStart: subDays(now, 7),
        prevStart: subDays(now, 14),
        prevEnd: subDays(now, 7),
      };
    case "30d":
      return {
        currentStart: subDays(now, 30),
        prevStart: subDays(now, 60),
        prevEnd: subDays(now, 30),
      };
    case "90d":
      return {
        currentStart: subDays(now, 90),
        prevStart: subDays(now, 180),
        prevEnd: subDays(now, 90),
      };
    case "365d":
      return {
        currentStart: subDays(now, 365),
        prevStart: subDays(now, 730),
        prevEnd: subDays(now, 365),
      };
    default:
      throw new Error("Invalid range");
  }
};

module.exports = {
  getDateRanges,
};
