const ejs = require("ejs");
const path = require("path");
const sgMail = require("@sendgrid/mail");
const { convert } = require("html-to-text");

sgMail.setApiKey(process.env.SENDGRID_API_KEY);

const from = `<PERSON><PERSON>an <${process.env.SENDGRID_SENDER_EMAIL}>`;

const sendmail = async (data) => {
  try {
    const { to, subject, template, content } = data;
    let htmldata = "";
    let text = "";

    if (template) {
      htmldata = await ejs.renderFile(
        path.join(__dirname, "../views/email", template),
        content,
      );

      text = convert(htmldata, {});
    }

    const msg = {
      from,
      to,
      cc: "<EMAIL>", // ToDo: remove this cc
      subject,
      text,
      html: htmldata || undefined,
    };

    const response = await sgMail.send(msg);

    return {
      success: true,
      statusCode: response[0]?.statusCode,
      message: "Email sent successfully",
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

module.exports = {
  sendmail,
};
