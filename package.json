{"name": "pipaan-node-admin", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js", "seed": "node ./seeder/index.js", "prettier:fix": "prettier --write ."}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@sendgrid/mail": "^8.1.4", "aws-sdk": "^2.1692.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "html-to-text": "^9.0.5", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mariadb": "^3.4.0", "moment": "^2.30.1", "morgan": "^1.10.0", "mysql2": "^3.14.0", "pg": "^8.14.1", "pg-hstore": "^2.3.4", "sequelize": "^6.37.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"nodemon": "^3.1.9", "prettier": "^3.5.3"}}