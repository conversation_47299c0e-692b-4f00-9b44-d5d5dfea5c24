/**
 * @swagger
 * components:
 *   schemas:
 *     Settings:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         app_name:
 *           type: string
 *           example: "Pipaan Admin"
 *         app_logo:
 *           type: string
 *           example: "https://example.com/logo.png"
 *         app_description:
 *           type: string
 *           example: "Admin panel for Pipaan application"
 *         contact_email:
 *           type: string
 *           format: email
 *           example: "<EMAIL>"
 *         contact_phone:
 *           type: string
 *           example: "+1234567890"
 *         address:
 *           type: string
 *           example: "123 Main Street, City, Country"
 *         social_media:
 *           type: object
 *           properties:
 *             facebook:
 *               type: string
 *               example: "https://facebook.com/pipaan"
 *             twitter:
 *               type: string
 *               example: "https://twitter.com/pipaan"
 *             instagram:
 *               type: string
 *               example: "https://instagram.com/pipaan"
 *         maintenance_mode:
 *           type: boolean
 *           example: false
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/settings:
 *   post:
 *     tags:
 *       - Settings
 *     summary: Get application settings
 *     description: Get current application settings
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Settings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Settings'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Failed to get settings
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /api/settings/edit:
 *   post:
 *     tags:
 *       - Settings
 *     summary: Update application settings
 *     description: Update application settings
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               app_name:
 *                 type: string
 *                 example: "Updated Pipaan Admin"
 *               app_logo:
 *                 type: string
 *                 example: "https://example.com/new-logo.png"
 *               app_description:
 *                 type: string
 *                 example: "Updated admin panel description"
 *               contact_email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               contact_phone:
 *                 type: string
 *                 example: "+9876543210"
 *               address:
 *                 type: string
 *                 example: "456 New Street, New City, Country"
 *               social_media:
 *                 type: object
 *                 properties:
 *                   facebook:
 *                     type: string
 *                     example: "https://facebook.com/newpipaan"
 *                   twitter:
 *                     type: string
 *                     example: "https://twitter.com/newpipaan"
 *                   instagram:
 *                     type: string
 *                     example: "https://instagram.com/newpipaan"
 *               maintenance_mode:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Settings updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Settings'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Failed to update settings
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
