/**
 * @swagger
 * /api/media/generate-presigned-url:
 *   post:
 *     tags:
 *       - Media
 *     summary: Generate presigned URL for file upload
 *     description: Generate a presigned URL for uploading files to AWS S3
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fileName
 *               - fileType
 *             properties:
 *               fileName:
 *                 type: string
 *                 example: "profile-image.jpg"
 *               fileType:
 *                 type: string
 *                 example: "image/jpeg"
 *               folder:
 *                 type: string
 *                 example: "uploads/profiles"
 *     responses:
 *       200:
 *         description: Presigned URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         uploadUrl:
 *                           type: string
 *                           example: "https://s3.amazonaws.com/bucket/presigned-url"
 *                         fileUrl:
 *                           type: string
 *                           example: "https://s3.amazonaws.com/bucket/file-url"
 *                         key:
 *                           type: string
 *                           example: "uploads/profiles/profile-image.jpg"
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Failed to generate presigned URL
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
