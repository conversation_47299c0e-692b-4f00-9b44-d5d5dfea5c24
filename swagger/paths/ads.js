/**
 * @swagger
 * components:
 *   schemas:
 *     Ad:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         title:
 *           type: string
 *           example: "Summer Sale Advertisement"
 *         description:
 *           type: string
 *           example: "Get 50% off on all summer items"
 *         image_url:
 *           type: string
 *           example: "https://example.com/ad-image.jpg"
 *         link_url:
 *           type: string
 *           example: "https://example.com/summer-sale"
 *         status:
 *           type: string
 *           enum: [active, inactive, draft]
 *           example: "active"
 *         start_date:
 *           type: string
 *           format: date
 *           example: "2024-06-01"
 *         end_date:
 *           type: string
 *           format: date
 *           example: "2024-08-31"
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/ads/list:
 *   post:
 *     tags:
 *       - Advertisements
 *     summary: Get ads list
 *     description: Get paginated list of advertisements
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ListRequest'
 *     responses:
 *       200:
 *         description: Ads retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         ads:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/Ad'
 *                         metadata:
 *                           $ref: '#/components/schemas/PaginationMeta'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Failed to get ads list
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /api/ads/detail:
 *   post:
 *     tags:
 *       - Advertisements
 *     summary: Get ad details
 *     description: Get details of a specific advertisement
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - adId
 *             properties:
 *               adId:
 *                 type: integer
 *                 example: 1
 *     responses:
 *       200:
 *         description: Ad details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Ad'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Ad not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Failed to get ad details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /api/ads/create:
 *   post:
 *     tags:
 *       - Advertisements
 *     summary: Create new advertisement
 *     description: Create a new advertisement
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Black Friday Sale"
 *               description:
 *                 type: string
 *                 example: "Huge discounts on all products"
 *               image_url:
 *                 type: string
 *                 example: "https://example.com/black-friday-ad.jpg"
 *               link_url:
 *                 type: string
 *                 example: "https://example.com/black-friday"
 *               status:
 *                 type: string
 *                 enum: [active, inactive, draft]
 *                 example: "active"
 *               start_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-11-25"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-11-30"
 *     responses:
 *       201:
 *         description: Ad created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         ad:
 *                           $ref: '#/components/schemas/Ad'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Failed to create ad
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /api/ads/edit:
 *   post:
 *     tags:
 *       - Advertisements
 *     summary: Update advertisement
 *     description: Update an existing advertisement
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - adId
 *             properties:
 *               adId:
 *                 type: integer
 *                 example: 1
 *               title:
 *                 type: string
 *                 example: "Updated Black Friday Sale"
 *               description:
 *                 type: string
 *                 example: "Even bigger discounts on all products"
 *               image_url:
 *                 type: string
 *                 example: "https://example.com/updated-black-friday-ad.jpg"
 *               link_url:
 *                 type: string
 *                 example: "https://example.com/updated-black-friday"
 *               status:
 *                 type: string
 *                 enum: [active, inactive, draft]
 *                 example: "active"
 *               start_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-11-25"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-11-30"
 *     responses:
 *       200:
 *         description: Ad updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         ad:
 *                           $ref: '#/components/schemas/Ad'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Ad not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Failed to update ad
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /api/ads/delete:
 *   post:
 *     tags:
 *       - Advertisements
 *     summary: Delete advertisement
 *     description: Delete an advertisement
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - adId
 *             properties:
 *               adId:
 *                 type: integer
 *                 example: 1
 *     responses:
 *       200:
 *         description: Ad deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Ad not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Failed to delete ad
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
