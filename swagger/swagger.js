const swaggerJSDoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");

// Swagger definition
const swaggerDefinition = {
  openapi: "3.0.0",
  info: {
    title: "Pipaan Admin API",
    version: "1.0.0",
    description: "API documentation for Pipaan Node Admin application",
    contact: {
      name: "API Support",
      email: "<EMAIL>",
    },
  },
  servers: [
    {
      url: process.env.API_BASE_URL || "http://localhost:8080",
      description: "Development server",
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        description: "Enter JWT token",
      },
    },
    schemas: {
      // Common response schemas
      SuccessResponse: {
        type: "object",
        properties: {
          success: {
            type: "boolean",
            example: true,
          },
          message: {
            type: "string",
            example: "Operation successful",
          },
          data: {
            type: "object",
          },
        },
      },
      ErrorResponse: {
        type: "object",
        properties: {
          success: {
            type: "boolean",
            example: false,
          },
          message: {
            type: "string",
            example: "Error message",
          },
          error: {
            type: "object",
          },
        },
      },
      // User related schemas
      User: {
        type: "object",
        properties: {
          id: {
            type: "integer",
            example: 1,
          },
          username: {
            type: "string",
            example: "johndoe123",
          },
          first_name: {
            type: "string",
            example: "John",
          },
          last_name: {
            type: "string",
            example: "Doe",
          },
          email: {
            type: "string",
            format: "email",
            example: "<EMAIL>",
          },
          mobile_number: {
            type: "string",
            example: "+1234567890",
          },
          status: {
            type: "string",
            enum: ["Active", "Inactive"],
            example: "Active",
          },
          role: {
            type: "integer",
            example: 1,
          },
          created_at: {
            type: "string",
            format: "date-time",
          },
          updated_at: {
            type: "string",
            format: "date-time",
          },
        },
      },
      LoginRequest: {
        type: "object",
        required: ["email", "password"],
        properties: {
          email: {
            type: "string",
            format: "email",
            example: "<EMAIL>",
          },
          password: {
            type: "string",
            example: "password123",
          },
        },
      },
      LoginResponse: {
        allOf: [
          { $ref: "#/components/schemas/SuccessResponse" },
          {
            type: "object",
            properties: {
              data: {
                type: "object",
                properties: {
                  accessToken: {
                    type: "string",
                    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                  },
                  refreshToken: {
                    type: "string",
                    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                  },
                  user: {
                    $ref: "#/components/schemas/User",
                  },
                },
              },
            },
          },
        ],
      },
      PaginationMeta: {
        type: "object",
        properties: {
          totalCount: {
            type: "integer",
            example: 100,
          },
          totalPages: {
            type: "integer",
            example: 10,
          },
          currentPage: {
            type: "integer",
            example: 1,
          },
          limit: {
            type: "integer",
            example: 10,
          },
        },
      },
      ListRequest: {
        type: "object",
        properties: {
          page: {
            type: "integer",
            minimum: 1,
            example: 1,
          },
          limit: {
            type: "integer",
            minimum: 1,
            maximum: 100,
            example: 10,
          },
          search: {
            type: "string",
            example: "search term",
          },
          sortBy: {
            type: "string",
            example: "created_at",
          },
          sortOrder: {
            type: "string",
            enum: ["asc", "desc"],
            example: "desc",
          },
        },
      },
    },
  },
  security: [
    {
      bearerAuth: [],
    },
  ],
};

// Options for the swagger docs
const options = {
  swaggerDefinition,
  // Path to the API docs
  apis: ["./routes/*.js", "./controller/*.js", "./swagger/paths/*.js"],
};

// Initialize swagger-jsdoc
const swaggerSpec = swaggerJSDoc(options);

module.exports = {
  swaggerUi,
  swaggerSpec,
};
