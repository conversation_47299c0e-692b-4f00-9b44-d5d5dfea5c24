// config/pg-db-config.js
const { Sequelize } = require("sequelize");
const dotenv = require("dotenv");

dotenv.config();

const pgSequelize = new Sequelize(
  process.env.PG_DB_DATABASE,
  process.env.PG_DB_USERNAME,
  process.env.PG_DB_PASSWORD,
  {
    host: process.env.PG_DB_HOST,
    dialect: process.env.PG_DB_DIALECT,
    port: process.env.PG_DB_PORT,
    logging: false,
  },
);

module.exports = { pgSequelize };

// models-pg/index.js
const fs = require("fs");
const path = require("path");
const { pgSequelize } = require("../config/pg-db-config");
const Sequelize = require("sequelize");

const db = {};
const modelsDir = path.join(__dirname, "..", "models");

fs.readdirSync(modelsDir)
  .filter((file) => file.endsWith(".js") && file !== "index.js")
  .forEach((file) => {
    const model = require(path.join(modelsDir, file))(
      pgSequelize,
      Sequelize.DataTypes,
    );
    db[model.name] = model;
  });

Object.keys(db).forEach((modelName) => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

db.pgSequelize = pgSequelize;
db.Sequelize = Sequelize;

module.exports = db;
