const express = require("express");
const router = express.Router();
const {
  getRoleList,
  getPermissionList,
  getRolePermissionList,
  updateRolePermissionList,
} = require("../controller/roleController");
const { authMiddleware } = require("../middleware/auth");

router.post("/list", authMiddleware, getRoleList);
router.post("/permissions", authMiddleware, getPermissionList);
router.post("/rolepermissions", authMiddleware, getRolePermissionList);
router.post("/rolepermissions/update", authMiddleware, updateRolePermissionList);

module.exports = router;
