const express = require("express");
const router = express.Router();
const {
  getAdsList,
  getAdDetail,
  createAd,
  editAd,
  deleteAd,
} = require("../controller/adsController");
const { authMiddleware } = require("../middleware/auth");

router.post("/list", authMiddleware, getAdsList);
router.post("/detail", authMiddleware, getAdDetail);
router.post("/create", authMiddleware, createAd);
router.post("/edit", authMiddleware, editAd);
router.post("/delete", authMiddleware, deleteAd);

module.exports = router;
