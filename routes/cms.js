const express = require("express");
const router = express.Router();
const {
  getCMSPages,
  getCMSById,
  getCMSPageBySlug,
  createCMSPage,
  updateCMSPage,
  deleteCMSPage,
} = require("../controller/cmsController");
const { authMiddleware } = require("../middleware/auth");

router.post("/list", authMiddleware, getCMSPages);
router.post("/detail-by-id", authMiddleware, getCMSById);
router.post("/detail", authMiddleware, getCMSPageBySlug);
router.post("/create", authMiddleware, createCMSPage);
router.post("/edit", authMiddleware, updateCMSPage);
router.post("/delete", authMiddleware, deleteCMSPage);

module.exports = router;
