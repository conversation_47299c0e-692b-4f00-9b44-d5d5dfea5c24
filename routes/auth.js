const express = require("express");
const router = express.Router();
const {
  login,
  forgotPassword,
  resetPassword,
  getUserData,
} = require("../controller/authController");
const { authMiddleware } = require("../middleware/auth");

router.post("/login", login);
router.post("/forgot-password", forgotPassword);
router.post("/reset-password", resetPassword);
router.post("/detail", authMiddleware, getUserData);

module.exports = router;
