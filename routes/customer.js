const express = require("express");
const router = express.Router();
const {
  getCustomersList,
  getCustomerDetail,
  createCustomer,
  editCustomer,
  deleteCustomer,
} = require("../controller/customerController");
const { authMiddleware } = require("../middleware/auth");

router.post("/list", authMiddleware, getCustomersList);
router.post("/detail", authMiddleware, getCustomerDetail);
router.post("/create", authMiddleware, createCustomer);
router.post("/edit", authMiddleware, editCustomer);
router.post("/delete", authMiddleware, deleteCustomer);

module.exports = router;
