const express = require("express");
const router = express.Router();
const {
  getDonationData,
  updateDonation,
  donationDetail,
} = require("../controller/donationController");
const { authMiddleware } = require("../middleware/auth");

router.post("/", authMiddleware, getDonationData);
router.post("/edit", authMiddleware, updateDonation);
router.post("/detail", authMiddleware, donationDetail);

module.exports = router;
