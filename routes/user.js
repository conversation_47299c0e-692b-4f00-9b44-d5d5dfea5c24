const express = require("express");
const router = express.Router();
const {
  getUserDetail,
  changePassword,
  getUserList,
  createUser,
  editUser,
  deleteUser,
  updateProfile,
} = require("../controller/userController");
const { authMiddleware } = require("../middleware/auth");

router.post("/list", authMiddleware, getUserList);
router.post("/detail", authMiddleware, getUserDetail);
router.post("/create", authMiddleware, createUser);
router.post("/edit", authMiddleware, editUser);
router.post("/delete", authMiddleware, deleteUser);
router.post("/change-password", authMiddleware, changePassword);
router.post("/update-profile", authMiddleware, updateProfile);
module.exports = router;
