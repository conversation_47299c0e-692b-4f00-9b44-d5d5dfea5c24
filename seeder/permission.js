const { Permission } = require("../models");

const seedPermissions = async () => {
  try {
    const permissions = [
      {
        name: "user_view",
        displayName: "User View",
        description: "Full access to manage users",
      },
      {
        name: "user_create",
        displayName: "User Create",
        description: "Full access to manage users",
      },
      {
        name: "user_edit",
        displayName: "User Edit",
        description: "Full access to manage users",
      },
      {
        name: "user_delete",
        displayName: "User Delete",
        description: "Full access to manage users",
      },
      {
        name: "customer_view",
        displayName: "Customer View",
        description: "Full access to manage users",
      },
      {
        name: "customer_create",
        displayName: "Customer Create",
        description: "Full access to manage customers",
      },
      {
        name: "customer_edit",
        displayName: "Customer Edit",
        description: "Full access to manage customers",
      },
      {
        name: "customer_delete",
        displayName: "Customer Edit",
        description: "Full access to manage customers",
      },
      {
        name: "donation_view",
        displayName: "Donation View",
        description: "Full access to manage donations",
      },
      {
        name: "donation_create",
        displayName: "Donation Create",
        description: "Full access to manage donations",
      },
      {
        name: "donation_edit",
        displayName: "Donation Edit",
        description: "Full access to manage donations",
      },
      {
        name: "live_metric",
        displayName: "Live Metric",
        description: "View live metrics",
      },
      {
        name: "user_analytics",
        displayName: "User Analytics",
        description: "View user analytics",
      },
      {
        name: "download_report",
        displayName: "Download Report",
        description: "Download reports",
      },
      {
        name: "ad_analytics_report",
        displayName: "Ad Analytics Report",
        description: "View ad analytics reports",
      },
      {
        name: "settings",
        displayName: "Settings",
        description: "Access and update application settings",
      },
    ];

    for (const data of permissions) {
      const exists = await Permission.findOne({ where: { name: data.name } });
      if (!exists) {
        await Permission.create(data);
        console.log(`Permission "${data.name}" added.`);
      } else {
        console.log(`Permission "${data.name}" already exists.`);
      }
    }
  } catch (error) {
    console.error("Error seeding permissions:", error);
  }
};

module.exports = seedPermissions;
