const { Role, Permission, RolePermission } = require("../models");

const seedRolePermissions = async () => {
  try {
    // Get roles and permissions by unique keys
    const roles = await Role.findAll();
    const permissions = await Permission.findAll();

    await RolePermission.destroy({ where: {} });

    for (const role of roles) {
      for (const permission of permissions) {
        await RolePermission.create({
          roleId: role.id,
          permissionId: permission.id,
          status: "active",
        });
      }
    }

    console.log("RolePermissions seeded with status 'active'.");
  } catch (error) {
    console.error("Error seeding RolePermissions:", error);
  }
};

module.exports = seedRolePermissions;
