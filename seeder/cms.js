const { Cms } = require("../models");

const seedCMSPages = async () => {
  try {
    const pages = [
      {
        title: "About Us",
        slug: "about-us",
        content:
          "<h1>About Us</h1><p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>",
        status: "Active",
      },
      {
        title: "Terms and Conditions",
        slug: "terms-and-conditions",
        content:
          "<h1>Terms and Conditions</h1><p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>",
        status: "Active",
      },
      {
        title: "Privacy Policy",
        slug: "privacy-policy",
        content:
          "<h1>Privacy Policy</h1><p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>",
        status: "Active",
      },
      {
        title: "Contact US",
        slug: "contact-us",
        content:
          "<h1>Privacy Policy</h1><p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>",
        status: "Active",
      },
    ];

    for (const pageData of pages) {
      const pageExists = await Cms.findOne({ where: { slug: pageData.slug } });
      if (!pageExists) {
        await Cms.create(pageData);
        console.log(`CMS page "${pageData.title}" added.`);
      } else {
        console.log(`CMS page "${pageData.title}" already exists.`);
      }
    }
  } catch (error) {
    console.error("Error seeding CMS pages:", error);
  }
};

module.exports = seedCMSPages;
