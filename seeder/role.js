const { Role } = require("../models");

const seedRoles = async () => {
  try {
    const roles = [
      {
        roleDisplayName: "Administrator",
        role: "admin",
        desc: "Full access to the system",
      },
      {
        roleDisplayName: "User",
        role: "user",
        desc: "Regular user with limited access",
      },
      {
        roleDisplayName: "Moderator",
        role: "moderator",
        desc: "Can manage user-generated content",
      },
    ];

    for (const roleData of roles) {
      const roleExists = await Role.findOne({ where: { role: roleData.role } });
      if (!roleExists) {
        await Role.create(roleData);
        console.log(`Role "${roleData.role}" added.`);
      } else {
        console.log(`Role "${roleData.role}" already exists.`);
      }
    }
  } catch (error) {
    console.error("Error seeding roles:", error);
  }
};

module.exports = seedRoles;
