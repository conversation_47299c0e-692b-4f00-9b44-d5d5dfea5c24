const { sequelize } = require("../models");
const seedRoles = require("./role");
const seedAdmin = require("./admin");
const seedPermissions = require("./permission");
const seedRolePermissions = require("./rolepermission");
const seedCMS = require("./cms");

const seedDatabase = async () => {
  try {
    // await sequelize.sync({ alter: true });
    console.log("Database synced!");

    await seedRoles();
    await seedAdmin();
    await seedPermissions();
    await seedRolePermissions();
    await seedCMS();

    console.log("Seeding completed!");
    process.exit(0);
  } catch (error) {
    console.error("Seeding failed:", error);
    process.exit(1);
  }
};

seedDatabase();
