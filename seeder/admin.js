const bcrypt = require("bcryptjs");
const { User, Role } = require("../models");

const seedAdmin = async () => {
  try {
    // Fetch the admin role
    const adminRole = await Role.findOne({ where: { role: "admin" } });
    if (!adminRole) {
      console.error("Admin role not found. Please run the role seeder first.");
      return;
    }

    // Check if the admin user already exists
    const adminExists = await User.findOne({
      where: { email: "<EMAIL>" },
    });
    if (adminExists) {
      console.log("Admin user already exists!");
      return;
    }

    await User.create({
      username: "admin",
      email: "<EMAIL>",
      password_hash: "<PERSON><PERSON><PERSON>@123",
      first_name: "Admin",
      last_name: "User",
      role: adminRole.id,
      status: "Active",
      is_email_verified: true,
      is_mobile_verified: true,
    });

    console.log("Admin user created successfully!");
  } catch (error) {
    console.error("Error seeding admin user:", error);
  }
};

module.exports = seedAdmin;
