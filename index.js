const express = require("express");
const dotenv = require("dotenv");
const cors = require("cors");
const morgan = require("morgan");
const { resolve, join } = require("path");
const { sequelize } = require("./config/db-config");
const routes = require("./routes/index");
const contents = require("./routes/content");
require("./models");

// Import Swagger configuration
const swaggerAdmin = require(resolve(join(__dirname, "swagger", "swagger")));

dotenv.config();
const PORT = process.env.PORT || 8080;

const app = express();
app.use(morgan("dev"));

app.use(cors());
app.use(express.json());

app.get("/", (req, res) => {
  res.send("Welcome to the Pipaan Admin API!");
});

// Swagger API documentation route
app.use(
  "/apidoc",
  swaggerAdmin.swaggerUi.serve,
  swaggerAdmin.swaggerUi.setup(swaggerAdmin.swaggerSpec, {
    explorer: true,
    customCss: ".swagger-ui .topbar { display: none }",
    customSiteTitle: "Pipaan Admin API Documentation",
  }),
);

app.use("/api", routes);

app.use("/cms", contents);

app.all("*", (req, res) => {
  res.status(404).json({ error: `Route ${req.originalUrl} not found` });
});

sequelize.sync().then(() => {
  console.log("Database connected");
  app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
});
