<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Password Reset Request</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f4f4f4;
      margin: 0;
      padding: 0;
    }
    .email-container {
      max-width: 600px;
      margin: 20px auto;
      background: #ffffff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .email-header {
      text-align: center;
      margin-bottom: 20px;
    }
    .email-header h1 {
      color: #333333;
    }
    .email-body {
      line-height: 1.6;
      color: #555555;
    }
    .email-body p {
      margin: 10px 0;
    }
    .reset-button {
      display: block;
      width: fit-content;
      margin: 20px auto;
      padding: 10px 20px;
      background-color: #007BFF;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      text-align: center;
      font-size: 16px;
    }
    .reset-button:hover {
      background-color: #0056b3;
    }
    .email-footer {
      text-align: center;
      margin-top: 20px;
      font-size: 12px;
      color: #aaaaaa;
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="email-header">
      <h1>Password Reset Request</h1>
    </div>
    <div class="email-body">
      <p>Hi <%= name %>,</p>
      <p>We received a request to reset your password for your account. You can reset your password by clicking the button below:</p>
      <a href="<%= resetPasswordUrl %>" class="reset-button">Reset Password</a>
      <p>This password reset link will expire in 10 minutes.</p>
      <p>Thanks,</p>
      <p>Pipaan Admin</p>
    </div>
    <div class="email-footer">
      <p>If the button above doesn't work, copy and paste this link into your browser:</p>
      <p><a href="<%= resetPasswordUrl %>"><%= resetPasswordUrl %></a></p>
    </div>
  </div>
</body>
</html>
